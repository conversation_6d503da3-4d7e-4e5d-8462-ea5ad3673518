spring:
  profiles:
    project:
      name: bluewind
    group:
      dev:
        - default
        - dev
        - oauth
      server:
        - default
        - server
        - oauth
      kang:
        - default
        - kang
        - oauth
---
spring:
  config:
    activate:
      on-profile:
        - default
  thymeleaf:
    cache: false
    check-template-location: true
    prefix: classpath:/templates/
    suffix: .html
  devtools:
    livereload:
      enabled: true
    restart:
      enabled: true
  freemarker:
    cache: false
  servlet:
    session:
      timeout: 30m
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
server:
  error:
    include-exception: false
    include-stacktrace: never
    include-message: never
    whitelabel:
      enabled: true
  compression:
    mime-types:
      -text/html
      -text/xml
      -text/plain
      -text/css
      -text/javascript
      -application/javascript
      -application/json
  tomcat:
    uri-encoding: UTF-8
mybatis:
  type-aliases-package: kr.co.wayplus.travel.model
  configuration:
    default-statement-timeout : 30
    auto-mapping-unknown-column-behavior : warning
    map-underscore-to-camel-case : true
  mapper-locations: classpath:/sql/
logging:
  file:
    path: "./logs/"
    name: "service.log"
  logback:
    rollingpolicy:
      max-history: 10
      max-file-size: 10MB
      file-name-pattern: "service.%d{yyyy-MM-dd}.%i.log"
using:
  spring:
    schedulerFactory: true
pg:
  nicepay:
    type: "nicepay"
    method: "forstart"
    isDevReal: false
    merchantKey: "EYzu8jGGMfqaDEp76gSckuvnaHHu+bC4opsSN6lHv3b2lurNYkVXrZ7Z1AoqQnXI3eLuaUFyoRNC6FkrzVjceg=="
    merchantID: "nicepay00m"
    clientID: "S2_5bf080abef064488a1391a7262aa89ee"
    secretKey: "805c2da9e83e455ba079153444210190"
    GoodsCl: 1
    TransType: 0
    CharSet: "utf-8"
    ReqResrved: ""
    logs-file-path: "/usr/local/tomcat/tomcat-10.1.13_travel/pglogs/nice_vacct_noti_result.log"
  tosspay:
    method: "tosspay"
    client:
      key: "live_gck_GjLJoQ1aVZ5QDWGkD5mw3w6KYe2R"
    server:
      key: "live_gsk_Z1aOwX7K8mWY7qxZz9z0VyQxzvNP"
#    client:
#      key: "test_gck_docs_Ovk5rk1EwkEbP0W43n07xlzm"
#    server:
#      key: "test_gsk_docs_OaPz8L5KdmQXkzRz3y47BMw6"
    #security:
    #  key: "baf96d22235580d1ced1dd22fc34a3d2e27e062cf36dab06fb3f6eb57c40aea0"
key:
  crypto:
    encrypt: "RandomEncryptKeyCodeValue"
    salt: "RandomSaltKeyCodeValue"
    iv: "RandomIvKeyCodeValue"
  api:
    kakao:
      native: "fd0cd64ee43a89387b8158eb11762f5a"
      rest: "2b4a9128a96d7dbc80c8a805d3ffb37f"
      javascript: "58154d69c3700d1b799f73113858e4e0"
      admin: "2ddb08011eee0b8686e377589c009672"
    naver:
      id: "lI7XopetaJOMs3o9WdlK"
      secret: "2YQ6c8KLyN"
    google:
      api: "AIzaSyDLINEku0dZ7_S51e5c_FnmamqXBjo8Tq0"
      oauth-id: "844930548483-193riljhchqm4lalvvi50peaflk4llt3.apps.googleusercontent.com"
      oauth-secret: "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"
    ncloud:
      ## 청풍용 key
      access: "ncp_iam_BPAMKR3037u1TgZ6UC0H"
      secret: "ncp_iam_BPKMKRH06CwVKByc1f684tSlBUNKONzyX5"
      SMS: "ncp:sms:kr:333076952728:ganghwauniverse"
      BizMessage: "ncp:kkobizmsg:kr:2710706:wayplus-infra"
      # access: "9yB5pBUin6bMGPPEPO4E"
      # secret: "R1AgJnnG2Gic6L2vBcdMEe1V6eUgAAv9JBLTrBQV"
      # BizMessage: "ncp:kkobizmsg:kr:2710706:wayplus-infra"
      # SMS: "ncp:sms:kr:271070672851:wayplus-infra"
trace: false
product:
  program:
    hide-minutes : 10
---
spring:
  config:
    activate:
      on-profile: "dev"
  datasource:
    hikari:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: ********************************************************
      #jdbc-url: ********************************************************
      username: bluewind
      password: dugoddjqrP2024!
      pool-name: Hikari Connection Pool
      minimum-idle: 2
      maximum-pool-size: 8
  servlet:
    multipart:
      max-file-size: 10MB #파일당
      max-request-size: 10MB #전체 파일
server:
  error:
    include-exception: true
    include-stacktrace: always
    include-message: always
  servlet:
    session:
      timeout: 60m
  address: 0.0.0.0
  port: 8081
upload:
  file:
    path: "c:/logs/upload/file/2024/travel/bluewind/"
    max-size: *********
cookie-set:
  domain: "localhost"
  prefix: "kr.co.wayplus."
  tracking: true
  tracking-age: 15638400
logging:
  file:
    path: "c:/logs/"
    name: "dev.log"
  logback:
    rollingpolicy:
      max-history: 10
      max-file-size: 100MB
      file-name-pattern: "dev.%d{yyyy-MM-dd}.%i.log"
pg:
  nicepay:
    isDevReal: false
    merchantKey: "EYzu8jGGMfqaDEp76gSckuvnaHHu+bC4opsSN6lHv3b2lurNYkVXrZ7Z1AoqQnXI3eLuaUFyoRNC6FkrzVjceg=="
    merchantID: "nicepay00m"
    clientID: "S2_5bf080abef064488a1391a7262aa89ee"
    secretKey: "805c2da9e83e455ba079153444210190"
    logs-file-path: "C:/logs/travel/pglogs/nice_vacct_noti_result.log"
  tosspay:
    method: "tosspay"
#    client:
#      key: "test_gck_Z1aOwX7K8meDm1K5ZAvm8yQxzvNP"
#    server:
#      key: "test_gsk_d46qopOB89Jv7G2RMAPo3ZmM75y0"
    client:
      key: "live_gck_GjLJoQ1aVZ5QDWGkD5mw3w6KYe2R"
    server:
      key: "live_gsk_Z1aOwX7K8mWY7qxZz9z0VyQxzvNP"
debug: true

---
spring:
  config:
    activate:
      on-profile: "server"
  datasource:
    hikari:
      driver-class-name: com.mysql.cj.jdbc.Driver
      # jdbc-url: **********************************************************
      jdbc-url: *************************************************
      username: bluewind
      password: dugoddjqrP2024!
      pool-name: Hikari Connection Pool
      minimum-idle: 2
      maximum-pool-size: 8
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
upload:
  file:
    path: "/usr/local/tomcat/external_upload/2024/travel/bluewind/"
    max-size: *********
cookie-set:
  domain: "www.guniverse.net"
  prefix: "net.guniverse.www."
  tracking: true
  tracking-age: 15638400
logging:
  file:
    path: "/logs/2024/travel/bluewind/"
    name: "bluewind.log"
  logback:
    rollingpolicy:
      max-history: 10
      max-file-size: 100MB
      file-name-pattern: "travelAgencyWeb.%d{yyyy-MM-dd}.%i.log"
server:
  address: www.guniverse.net
  port: 80
  servlet:
    context-path: /

---
spring:
  config:
    activate:
      on-profile: "kang"
  datasource:
    hikari:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: ******************************************************
      username: bluewind
      password: dugoddjqrP2024!
      pool-name: Hikari Connection Pool
      minimum-idle: 2
      maximum-pool-size: 8
server:
  port: 80
upload:
  file:
    path: "c:/logs/upload/file/bluewind/"
    max-size: *********
cookie-set:
  #domain: "localhost"
  #prefix: "kr.wayplus."
  domain: "localhost"
  prefix: "kr.wayplus."
  tracking: true
  tracking-age: 15638400