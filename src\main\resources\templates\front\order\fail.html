<!DOCTYPE html>
<html lang="ko" xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title th:text="${company != null} ? ${company.siteName}">#{여행사}</title>
    <head th:replace="~{front/inc/common-header :: include}" />
	<head th:replace="~{front/inc/header :: header-script}" />
</head>
<body>
	<header th:replace="~{front/inc/header :: header}" />
	
	<section class="wrap">
		<div class="payment-loading-wrap">
			<div class="contents">
				<div class="payment-loading">
					<h4 th:text="${message != null ? message : '결제가 취소되었습니다.'}">결제가 취소되었습니다.</h4>
					<p th:if="${message == null}">주문번호 : <span id="orderId"></span></p>
					<p id="message"></p>
				</div><!--payment-loading-->
			</div><!--contents-->
		</div><!--payment-loading-wrap-->
	</section>

	<footer th:replace="~{front/inc/footer :: footer-bluewind}" />
	<div th:replace="~{front/inc/header :: progressbar-popup}" />

	<script src="https://js.tosspayments.com/v2/standard"></script>
	<script th:inline="javascript">
		var requestData = [[${requestData}]]||{};
		var responseData = [[${responseData}]]||{};
		
		var reqJson = JSON.parse(requestData);
		var resJson = JSON.parse(responseData);
		
		$(document).ready(function(){
			console.log(requestData);
			console.log(responseData);
			
			$('#orderId').html( reqJson.orderId );
			$('#message').html( resJson.message );
			
			
		})
	</script>

	<style>
		.wrap{min-height:665px;}
		.order_area {padding-top: 80px;position: relative;}
		.order_area .order_menu { max-width: 1280px; margin: 0 auto; overflow: hidden; margin-bottom: 32px;}
		.order_area .order_area_title { overflow: hidden; /*margin-bottom: 32px;*/ }
		.order_area .order_area_title .more_box { display: block;  float: right; width: 86px; height: 33px; border: 1px solid #CCCCCC; box-sizing: border-box; border-radius: 16px; position: relative; top: -30px;}
		.order_area .order_area_title .more_box.active {display:inline-block;}
		.order_area .order_area_title .more_box:hover span {border-bottom: 1px solid #666666;cursor: pointer;}

		.order_area .order_area_grp {display: flex;justify-content: center;flex-direction: column;align-items: flex-start;font-weight: 400;}
		.order_area .order_area_txt {color:#222;font-size:40px;font-weight:600;line-height: 38px;}
		.order_area .order_area_desc {margin-top:43px; font-size:18px;color:#333;}
		.order_area .order_area_desc p{text-align: center;line-height: 28px;}

		.order_form {max-width: 1280px; display: flex;flex-direction: column; justify-content: center; margin: 84px auto }
		.order_form .title{ color: #222;font-size: 30px;font-style: normal;font-weight: 600;margin-bottom: 20px; }

		.order_form .control{ height: 35px; border-radius: 5px;border: 1px solid #AAA; background-color: #fff; width: 100%; }

		.order_area .product_list{ flex-direction: column; border-top: 1px solid #333;max-width: 900px;margin: 0 auto 80px auto; }
		.order_area .product_info{padding: 20px 0;margin: 20px auto 0 auto;width: 100%;border-bottom: 1px solid #ccc;}
		.order_area .alert_msg{margin: 20px auto 0 auto;width: 100%;display: flex;align-items: center;}

		.btn_box {display: flex;justify-content: center;}
		.btn_box .way-btn{width: 399px;height: 60px;font-size: 20px; margin-right: 10px;}
		.btn_box .way-btn.upd{width: 132px;height: 41px;font-size: 16px;font-weight: 400;}

		.detail_line{ width: 100%;border-bottom: 1px solid #000; margin: 15px 0;}
		.detail_line.gray{ border-color: #ccc;}
		.detail_form_box .detail_line{ width: 100%;border-bottom: 1px solid #ccc; margin-top: 11px;margin-bottom: 15px;}
		.detail_line.deshed{ border-bottom: 1px dashed #ccc;}

		.row{display: flex;}
		.row.col{flex-direction: column;}
		.row.aic{align-items: center;}
		.row.jcc{justify-content: center;}
		.row.jcfe,
		.btn_box.jcfe{justify-content: flex-end;}
		.row.jcse{justify-content: space-evenly;}

		.circle{width: 59px; height: 59px; border-radius:50%;display: flex;justify-content: center;align-items: center;}
		.circle.green{background-color: #47691F;}

		.item{padding: 5px 0;font-size: 18px;font-weight: 400;}
		.item label{font-weight: 600;}
		.item span{ padding:0 10px; }
		.item span:not(:last-child):after{content: '|';padding-left: 10px;}
		.item .reservater{border: 1px solid #965841;border-radius:3px; color: #965841;padding: 5px;}

		.product_info {padding-left: 80px;align-items: flex-start;max-width: 1200px;}
		.product_info .detail_desc_box {display: flex;flex-direction: column;justify-content: space-between;height: 100%;}
		.product_info .detail_line{margin: 20px 0;}

		.product_info {display: flex;align-items: center;/*margin:0 auto;*/padding-bottom: 30px;margin-bottom:30px;}
		.product_info img{max-width: 137px;max-height: 112px;object-fit: cover;}
		.product_chk {width: 22px;height: 22px;margin-right: 10px;}
		.product_text {width: calc(100% - 247px);padding: 0 29px;}
		.product_text p{line-height: 24px; font-size: 14px;font-weight: 400;padding-bottom: 10px;display: flex; align-items: center;}
		.product_text .product_title{font-size: 18px;font-weight: 600;line-height: 34px;}
		.product_link {display: flex;justify-content: center;align-items: center;flex-direction: column;;}
		.product_link .btn {width: 116px;height: 36px;border-radius: 50px;background: #fff;font-size: 15px;font-weight: 500;margin-bottom: 5px;cursor: pointer;}

	</style>

</body>
</html>