package kr.co.wayplus.travel.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

/**
 * 상품 관련 설정을 관리하는 Configuration 클래스
 */
@Component
@ConfigurationProperties(prefix = "product")
@Getter
@Setter
public class ProductConfig {
    
    private Program program = new Program();
    
    @Getter
    @Setter
    public static class Program {
        /**
         * 프로그램 예약 시 숨김 처리할 분 단위
         * 현재 시간으로부터 이 값만큼 이전까지는 예약 가능
         */
        private int hideMinutes = 10; // 기본값 10분
    }
}
